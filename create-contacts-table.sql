-- Create contacts table for storing contact form submissions
USE hobby_project;

CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
);

-- Insert some test data (optional)
-- INSERT INTO contacts (email, message) VALUES 
-- ('<EMAIL>', 'This is a test message'),
-- ('<EMAIL>', 'Another test message');
