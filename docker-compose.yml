services:

  mongodb:
    image: mongo:latest
    container_name: mongodb-container
    environment:
      MONGO_INITDB_DATABASE: mydb
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db

  postgres:
    image: postgres:latest
    container_name: postgres-container
    environment:
      POSTGRES_DB: mydb
      POSTGRES_USER: test
      POSTGRES_PASSWORD: 123
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data

  mssql:
    image: mcr.microsoft.com/mssql/server:latest
    container_name: mssql-container
    environment:
      SA_PASSWORD: "ComplexPass123!"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - mssql-data:/var/opt/mssql

  mysql:
    image: mysql:latest
    container_name: mysql-container
    environment:
      MYSQL_ROOT_PASSWORD: 123
      MYSQL_DATABASE: mydb
      MYSQL_USER: test
      MYSQL_PASSWORD: 123
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql

  redis:
    image: redis:latest
    container_name: redis-container
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  mariadb:
    image: mariadb:latest
    container_name: mariadb-container
    environment:
      MYSQL_ROOT_PASSWORD: 123
      MYSQL_DATABASE: mydb
      MYSQL_USER: test
      MYSQL_PASSWORD: 123
    ports:
      - "3308:3306"
    volumes:
      - mariadb-data:/var/lib/mysql

volumes:
  mongodb-data:
  postgres-data:
  mssql-data:
  mysql-data:
  redis_data:
  mariadb-data: