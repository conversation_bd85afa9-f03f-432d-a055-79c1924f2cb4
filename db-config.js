// Database configuration
const mysql = require('mysql2/promise');

const dbConfig = {
    host: 'localhost',
    port: 3308, // Port matching your Docker container
    user: 'root',
    password: '123',
    database: 'hobby_project'
};

// Create connection pool
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// Test database connection
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        console.log('✅ Database connected successfully');
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
}

module.exports = {
    pool,
    testConnection
};
