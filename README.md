# LYZELL's POTTERY

This webpage showcases a pottery artist's work with a complete overview of her ceramic creations.

## Features
- Home page with welcome gallery
- About Me page with artist's story
- Completed pieces gallery with pottery collection
- Contact page with business details and inquiry form

## Setup
1. Start database: `docker-compose up -d`
2. Start application: `npm start`
3. Access: http://localhost:3000

## Project Structure
```
├── public/                 # Static frontend files
│   ├── index.html         # Main homepage
│   ├── aboutme.html       # About Me page
│   ├── contact.html       # Contact page with form
│   ├── completedpieces.html # Completed works gallery
│   ├── header.html        # Shared header component
│   └── images/            # Pottery images and assets
├── server.js              # Express.js server configuration
├── docker-compose.yml     # Docker services configuration
├── package.json           # Node.js dependencies and scripts
└── README.md              # Project documentation
```

## Technical Stack
- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Node.js with Express.js framework
- **Database**: MariaDB (containerized with Docker)
- **Static Assets**: Images and styling served through Express
