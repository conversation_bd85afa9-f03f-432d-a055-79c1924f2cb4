// server.js
const express = require('express');
const path = require('path');
const { pool, testConnection } = require('./db-config');

const app = express();
const port = 3000;

// Middleware to parse form data
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Serve static files from the 'public' directory
app.use(express.static('public'));

// Define routes for your HTML files
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/about', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'aboutme.html'));
});

app.get('/contact', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'contact.html'));
});

app.get('/completedpieces', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'completedpieces.html'));
});

// Handle contact form submission
app.post('/contact', async (req, res) => {
    const { email, message } = req.body;

    try {
        // Save to database (optional - create a contacts table if needed)
        // For now, just log the data
        console.log('Contact form submission received:');
        console.log('Email:', email);
        console.log('Message:', message);
        console.log('Timestamp:', new Date().toISOString());

        // You can uncomment this when you have a contacts table:
        /*
        const [result] = await pool.execute(
            'INSERT INTO contacts (email, message, created_at) VALUES (?, ?, NOW())',
            [email, message]
        );
        console.log('Saved to database with ID:', result.insertId);
        */

        // Send a JSON response indicating success
        res.json({
            success: true,
            message: 'Teie sõnum on edukalt saadetud! Võtame teiega peagi ühendust.'
        });
    } catch (error) {
        console.error('Error handling contact form:', error);
        res.status(500).json({
            success: false,
            message: 'Viga sõnumi saatmisel. Palun proovige hiljem uuesti.'
        });
    }
});

// Start the server
app.listen(port, async () => {
    console.log(`Server is running on http://localhost:${port}`);

    // Test database connection
    await testConnection();
});