<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lyzell's Pottery</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
        }

        h1 {
            font-size: 3em;
            margin-top: 20px;
        }

        .home-button {
            margin: 20px;
            padding: 10px 20px;
            font-size: 1em;
            border: 2px solid black;
            background-color: white;
            cursor: pointer;
        }

        .image-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 20px;
        }

        .image-column {
            display: flex;
            flex-direction: column;
            margin: 10px;
        }

        /* Force each image to be 200x200, cropping as needed */
        .image-column img {
            width: 200px;
            height: 200px;
            object-fit: cover;
            margin: 10px;
        }

        .welcome-box {
            margin: 20px;
            padding: 30px;
            border: 2px solid blue;
            display: inline-block;
            font-size: 1.2em;
            color: #555;
            max-width: 600px;
            text-align: center;
            line-height: 1.5;
            background-color: #f9f9f9;
        }

        .social-media {
            margin-top: 20px;
        }

        .social-media a {
            text-decoration: none;
            color: black;
            margin: 10px;
        }

        .social-media button {
            border: 2px solid lightgray;
            background-color: white;
            padding: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div id="header"></div>

<script>
    fetch('header.html')
        .then(response => response.text())
        .then(data => {
            document.getElementById('header').innerHTML = data;
        });
</script>

<div class="image-container">
    <div class="image-column">
        <img src="images/pottery1.jpg" alt="Pottery 1">
        <img src="images/pottery2.jpg" alt="Pottery 2">
    </div>
    <div>
        <div class="welcome-box">
            Welcome to LYZELL's POTTERY - where clay transforms into art! Discover our unique handcrafted ceramic pieces, learn about our artistic journey, and explore the beauty of traditional pottery techniques. Each piece tells a story of passion, creativity, and dedication to the timeless craft of ceramics.
        </div>
    </div>
    <div class="image-column">
        <img src="images/pottery3.jpg" alt="Pottery 3">
        <img src="images/pottery4.jpg" alt="Pottery 4">
    </div>
</div>

<div class="social-media">
    <a href="https://facebook.com/lyzellspottery" target="_blank">
        <button>Facebook - Ly'Pottery</button>
    </a>
    <a href="https://instagram.com/lyzellspottery" target="_blank">
        <button>Instagram - Ly'Pottery</button>
    </a>
</div>
</body>
</html>
